<?php

namespace App\Jobs\Export;

use App\Exports\OrderRowsForERPExport;
use App\Jobs\Export\Concerns\LogsExportProgress;
use App\Models\Order\Order;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ExportOrderRowsJob implements ShouldQueue
{
    use Queueable, LogsExportProgress;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     */
    public $timeout = 120;

    /**
     * The order to export.
     */
    public Order $order;

    /**
     * The export ID for logging.
     */
    public int $exportId;

    /**
     * The timestamp for file naming.
     */
    public string $timestamp;

    /**
     * The file type for logging.
     */
    public string $fileType = 'order_rows';

    /**
     * Create a new job instance.
     */
    public function __construct(Order $order, int $exportId, string $timestamp)
    {
        $this->order = $order;
        $this->exportId = $exportId;
        $this->timestamp = $timestamp;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Starting order rows export", [
                'order_id' => $this->order->id,
                'export_id' => $this->exportId,
            ]);

            // Check if order has rows
            if ($this->order->orderRows()->count() === 0) {
                throw new \Exception("Order {$this->order->id} has no order rows");
            }

            // Generate file name
            $fileName = "order_{$this->order->id}_{$this->timestamp}.xlsx";
            $filePath = "export-proforma/{$fileName}";

            // Create the export
            $export = new OrderRowsForERPExport($this->order);

            // Store the file to the exchange disk
            Excel::store($export, $filePath, config('filesystems.exchange', 'exchange'));

            // Verify the file was created
            if (!Storage::disk(config('filesystems.exchange', 'exchange'))->exists($filePath)) {
                throw new \Exception("Failed to create order rows file: {$filePath}");
            }

            Log::info("Order rows export completed", [
                'order_id' => $this->order->id,
                'export_id' => $this->exportId,
                'file_path' => $filePath,
            ]);

            // Log success
            $this->logSuccess($filePath);

        } catch (\Exception $e) {
            Log::error("Failed to export order rows", [
                'order_id' => $this->order->id,
                'export_id' => $this->exportId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }
}
