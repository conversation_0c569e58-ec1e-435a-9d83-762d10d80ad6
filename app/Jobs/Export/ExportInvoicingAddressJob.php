<?php

namespace App\Jobs\Export;

use App\Exports\InvoicingAddressExport;
use App\Jobs\Export\Concerns\LogsExportProgress;
use App\Models\Order\Order;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ExportInvoicingAddressJob implements ShouldQueue
{
    use Queueable, LogsExportProgress;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     */
    public $timeout = 120;

    /**
     * The order to export.
     */
    public Order $order;

    /**
     * The export ID for logging.
     */
    public int $exportId;

    /**
     * The timestamp for file naming.
     */
    public string $timestamp;

    /**
     * The file type for logging.
     */
    public string $fileType = 'invoicing_address';

    /**
     * Create a new job instance.
     */
    public function __construct(Order $order, int $exportId, string $timestamp)
    {
        $this->order = $order;
        $this->exportId = $exportId;
        $this->timestamp = $timestamp;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Starting invoicing address export", [
                'order_id' => $this->order->id,
                'export_id' => $this->exportId,
            ]);

            // Get the invoicing address
            $invoicingAddress = $this->order->invoicingAddress;
            
            if (!$invoicingAddress) {
                throw new \Exception("Order {$this->order->id} has no invoicing address");
            }

            // Generate file name
            $fileName = "invoicing_address_{$invoicingAddress->id}_{$this->timestamp}.xlsx";
            $filePath = "export-anagrafiche/{$fileName}";

            // Create the export
            $export = new InvoicingAddressExport($invoicingAddress, $this->order);

            // Store the file to the exchange disk
            Excel::store($export, $filePath, config('filesystems.exchange', 'exchange'));

            // Verify the file was created
            if (!Storage::disk(config('filesystems.exchange', 'exchange'))->exists($filePath)) {
                throw new \Exception("Failed to create invoicing address file: {$filePath}");
            }

            Log::info("Invoicing address export completed", [
                'order_id' => $this->order->id,
                'export_id' => $this->exportId,
                'file_path' => $filePath,
            ]);

            // Log success
            $this->logSuccess($filePath);

        } catch (\Exception $e) {
            Log::error("Failed to export invoicing address", [
                'order_id' => $this->order->id,
                'export_id' => $this->exportId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }
}
