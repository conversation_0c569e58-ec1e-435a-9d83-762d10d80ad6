<?php

namespace App\Observers;

use App\Models\Order\Order;
use App\Models\Project\Project;

class OrderObserver
{
    /**
     * Handle the Order "creating" event.
     */
    public function creating(Order $order)
    {
        // Get the year from the order date
        $year = $order->date->format('Y');

        // Get the last progressive code for the year
        $lastProgressive = Order::whereYear('date', $year)
            ->max('code_progressive') ?? 0;

        // Set the code progressive and the code
        $order->code_progressive = $lastProgressive + 1;
        $order->code = "{$order->code_progressive}/{$year}";
    }
    
    /**
     * Handle the Order "created" event.
     */
    public function created(Order $order): void
    {
        // Check if the order is linked to a project
        if ($order->project_id) {
            // Set the order code to the project code
            $project = Project::find($order->project_id);
            $order->order_code = $project->project_code;
        }
        else {
            // Set the order code based on the order code type and region
            $type = is_object($order->order_code_type) ? $order->order_code_type->code() : '';
            $region = is_object($order->order_code_region) ? $order->order_code_region->code() : '';
            $date = $order->order_code_date ? $order->order_code_date->format('y') : '';
            $progressive = str_pad($order->order_code_progressive, 6, '0', STR_PAD_LEFT);
    
            $order->order_code = $order->order_code_progressive
                ? "{$type}_{$region}{$date}-{$progressive}"
                : null;
        }
        
        $order->saveQuietly();  
    }

    /**
     * Handle the Order "updated" event.
     */
    public function updated(Order $order): void
    {
        // Check if the order is linked to a project
        if ($order->project_id) {
            // Set the order code to the project code
            $project = Project::find($order->project_id);
            $order->order_code = $project->project_code;
        }
        else {
            // Set the order code based on the order code type and region
            $type = is_object($order->order_code_type) ? $order->order_code_type->code() : '';
            $region = is_object($order->order_code_region) ? $order->order_code_region->code() : '';
            $date = $order->order_code_date ? $order->order_code_date->format('y') : '';
            $progressive = str_pad($order->order_code_progressive, 6, '0', STR_PAD_LEFT);

            $order->order_code = $order->order_code_progressive
                ? "{$type}_{$region}{$date}-{$progressive}"
                : null;
        }
        
        $order->saveQuietly();
    }

    /**
     * Handle the Order "deleted" event.
     */
    public function deleted(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "restored" event.
     */
    public function restored(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "force deleted" event.
     */
    public function forceDeleted(Order $order): void
    {
        //
    }
}
