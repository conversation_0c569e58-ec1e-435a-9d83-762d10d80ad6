<?php

namespace App\Exports;

use App\Models\Order\Order;
use App\Models\Order\OrderRow;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithDrawings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;

class OrderExport implements FromQuery, WithHeadings, WithMapping, WithColumnFormatting, WithDrawings, WithEvents
{
    protected $order;
    protected $rows = [];
    protected $rowIndex = 0;
    protected $rowHeight = 60; // Row height in points

    private $temporaryFiles;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }
    
    public function query()
    {
        // Get cart group IDs associated with this order
        $cartGroupIds = $this->order->cartGroups()->pluck('id')->toArray();
        
        // Filter order rows by these cart group IDs
        return OrderRow::query()->whereIn('cart_group_id', $cartGroupIds)->orderBy('sort');
    }

    public function headings(): array
    {
        return [
            'FLOOR & LOCATION',
            'ID CODE',    
            'OPTION',
            'BRAND',
            'IMAGE',
            'MODEL',
            'DESCRIPTION',
            'CODE',
            'FINISHES',
            'DIMENSIONS',
            'QTY',
            'M.C.',
            'UNIT PRICE',
            'TOTAL PRICE',
            'BG UNIT PRICE',
            'BG TOTAL PRICE',
            'DISCOUNT',
            'VARIATION',
            'UNIT DISCOUNTED PRICE',
            'TOTAL DISCOUNTED PRICE',
            'PROVV. PARTNER',
            'BG INCOME',
            'LEAD TIME',
            'NOTES'
        ];
    }

    public function map($row): array
    {
        // Store the row for later use in drawings
        $this->rows[$this->rowIndex++] = $row;
        
        // Build the options string if the row has options
        if ($row->options) {
            $options = $row->getSelectedOptions();
            $optionDescriptions = array_map(function ($option) {
                return $option['description'];
            }, $options);
        }
        
        return [
            // FLOOR & LOCATION
            $row->cartGroup->name ?? '',

            // ID CODE
            $row->position_id ?? '',

            // OPTION
            '', // EMPTY

            // BRAND
            $row->product?->brand->name ?? $row->customProduct?->brand_name ?? '',

            // IMAGE
            '', // Populated with WithDrawings

            // MODEL
            $row->product?->collection->name ?? '',

            // DESCRIPTION
            $row->description ?? $row->product?->description ?? $row->customProduct?->description ?? '',
            
            // CODE
            $row->sku ?? ($row->options ? $row->getModularSku() : $row->product?->sku ?? $row->customProduct?->sku ?? ''),

            // FINISHES
            $optionDescriptions ?? '',
            
            // DIMENSIONS
            $row->product->dimensions ?? $row->customProduct->dimensions ?? '',

            // QTY
            $row->quantity ?? '',
            
            // M.C.
            $row->purchase_units ?? '',
            
            // UNIT PRICE
            $row->selling_price ?? '',
            
            // TOTAL PRICE
            $row->selling_price * $row->quantity ?? '',

            // BG UNIT PRICE
            $row->selling_price * (1 - ($row->product?->discountGroup?->discount ?? 0) / 100) ?? '',

            // BG TOTAL PRICE
            $row->selling_price * $row->quantity * (1 - ($row->product?->discountGroup?->discount ?? 0) / 100) ?? '',
            
            // DISCOUNT
            isset($row->discount_override) ? $row->discount_override : ($row->discount ?? $row->getDiscountForClient()),
            
            // VARIATION
            $row->variation ?? '',
            
            // UNIT DISCOUNTED PRICE
            $row->rowUnitPrice ?? '',
            
            // TOTAL DISCOUNTED PRICE
            $row->rowFinalPrice ?? '',
            
            // PROVV. PARTNER
            '', // EMPTY
            
            // BG INCOME
            $row->rowFinalPrice - ($row->selling_price * $row->quantity * (1 - ($row->product?->discountGroup?->discount ?? 0) / 100)) ?? '',
            
            // LEAD TIME
            $row->expected_delivery_date?->format('d-m-y') ?? '-',
            
            // NOTES
            '', // EMPTY
        ];
    }

    public function columnFormats(): array
    {
        return [

        ];
    }

    public function drawings()
    {
        $this->temporaryFiles = [];

        $drawings = [];
        
        foreach ($this->rows as $index => $row) {
            $imagePath = null;
            
            if ($row->product && $row->product->image && Storage::disk(config('filesystems.public'))->exists($row->product->image)) {
                $imagePath = $this->getLocalImagePath($row->product->image);
            } elseif ($row->customProduct && $row->customProduct->image && Storage::disk(config('filesystems.public'))->exists($row->customProduct->image)) {
                $imagePath = $this->getLocalImagePath($row->customProduct->image);
            }
            
            if ($imagePath) {
                $drawing = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                $drawing->setName('Product Image');
                $drawing->setDescription('Product Image');
                $drawing->setPath($imagePath);
                $drawing->setHeight(50);
                $drawing->setResizeProportional(true);
                $drawing->setCoordinates('E' . ($index + 2));
                $drawing->setOffsetX(2);
                $drawing->setOffsetY(2);

                $drawings[] = $drawing;
            }
        }

        return $drawings;
    }

    private function getLocalImagePath($path)
    {
        $diskName = config('filesystems.public');
        $disk = Storage::disk($diskName);
        $diskDriver = config("filesystems.disks.{$diskName}.driver");
    
        if ($diskDriver === 's3') {
            $tmpPath = storage_path('app/tmp/' . basename($path));
            if (!file_exists(dirname($tmpPath))) {
                mkdir(dirname($tmpPath), 0755, true);
            }
            file_put_contents($tmpPath, $disk->get($path));
            
            $this->temporaryFiles[] = $tmpPath;
    
            return $tmpPath;
        }
    
        return $disk->path($path);
    }

    public function __destruct()
    {
        if (!empty($this->temporaryFiles)) {
            foreach ($this->temporaryFiles as $file) {
                if (file_exists($file)) {
                    @unlink($file);
                }
            }
        }
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                // Set row height for header and all data rows
                $event->sheet->getRowDimension(1)->setRowHeight($this->rowHeight);
                
                foreach ($this->rows as $index => $row) {
                    $rowNumber = $index + 2;
                    $event->sheet->getRowDimension($rowNumber)->setRowHeight($this->rowHeight);
                }
                
                // Set the first row (header) in bold
                $event->sheet->getStyle('A1:X1')->getFont()->setBold(true);
                
                // Apply background color to header without custom borders
                $event->sheet->getStyle('A1:X1')->applyFromArray([
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'color' => ['argb' => 'FFE6E6E6'], // Light gray background
                    ],
                    'alignment' => [
                        'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                    ],
                ]);
                
                // Set vertical alignment to middle for all cells
                $lastRow = count($this->rows) + 1;
                $event->sheet->getStyle('A1:X' . $lastRow)->getAlignment()->setVertical(
                    \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
                );
                
                // Auto-size all columns to fit content
                foreach (range('A', 'X') as $column) {
                    $event->sheet->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ];
    }
}
