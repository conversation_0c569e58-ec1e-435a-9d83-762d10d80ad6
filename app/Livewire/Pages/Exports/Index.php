<?php

namespace App\Livewire\Pages\Exports;

use App\Models\Export;
use App\Models\Product;
use Livewire\Component;
use Illuminate\View\View;
use Livewire\WithPagination;
use App\Models\CustomProduct;
use Spatie\SimpleExcel\SimpleExcelWriter;

class Index extends Component
{
    use WithPagination;

    /**
     * Search term for filtering exports
     */
    public string $search = '';

    /**
     * Modal state for showing errors
     */
    public bool $showErrorModal = false;

    /**
     * Current export for error display
     */
    public ?Export $selectedExport = null;

    /**
     * Get exports with pagination and search
     */
    public function getExportsProperty()
    {
        return Export::with(['user', 'errors', 'resource'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('type', 'like', '%' . $this->search . '%')
                      ->orWhere('resource_type', 'like', '%' . $this->search . '%')
                      ->orWhereHas('user', function ($userQuery) {
                          $userQuery->where('email', 'like', '%' . $this->search . '%');
                      });
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);
    }

    /**
     * Get active exports count
     */
    public function getActiveExportsCountProperty(): int
    {
        return Export::active()->count();
    }

    /**
     * Clear search
     */
    public function clearSearch(): void
    {
        $this->search = '';
        $this->resetPage();
    }

    /**
     * Updated search property
     */
    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    /**
     * Show errors modal for an export
     */
    public function showErrors(int $exportId): void
    {
        $this->selectedExport = Export::with('errors')->find($exportId);
        $this->showErrorModal = true;
    }

    /**
     * Close errors modal
     */
    public function closeErrorModal(): void
    {
        $this->showErrorModal = false;
        $this->selectedExport = null;
    }

    /**
     * Render the component
     */
    public function render(): View
    {
        return view('livewire.pages.exports.index', [
            'exports' => $this->exports,
        ]);
    }

    /**
     * Download custom products export
     */
    public function downloadCustomProducts(): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $products = CustomProduct::all();
        $fileName = 'custom-products.xlsx';

        $writer = SimpleExcelWriter::streamDownload($fileName);

        foreach ($products as $index => $product) {
            $writer->addRow([
                'CREATED BY' => $product->user?->email ?? '',
                'TEMPORARY SKUS' => $product->id,
                'SKU' => $product->sku,
                'ADHOC SKU' => '',
                'BRAND NAME' => $product->brand_name,
                'BRAND PREFIX' => '',
                'COLLECTION CODE' => '',
                'COLLECTION NAME' => '',
                'SUPPLIER COLOR' => $product->supplier_color,
                'COLOR' => '',
                'DESCRIPTION BRG' => $product->description,
                'DESTINATION ROOM' => '',
                'DIMENSION' => $product->dimensions,
                'DISCOUNT GROUP' => '',
                'ITEM STATUS' => '',
                'LEAD TIME' => '',
                'MATERIAL' => '',
                'PRODUCT TYPE' => '',
                'PURCHASE UNIT' => '',
                'SELLING PRICE' => '',
            ]);

            // Flush if over 1000 loop iterations
            if ($index % 1000 === 0) {
                flush();
            }
        }

        return response()->streamDownload(function () use ($writer) {
            $writer->close();
        }, $fileName);
    }

    /**
     * Download missing discount group products export
     */
    public function downloadMissingDiscountGroup(): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $products = Product::whereDoesntHave('discountGroup')->get();
        $fileName = 'missing-discount-group-products.xlsx';

        $writer = SimpleExcelWriter::streamDownload($fileName);

        foreach ($products as $product) {
            $writer->addRow([
                'id' => $product->id,
                'sku' => $product->sku,
            ]);
        }

        return response()->streamDownload(function () use ($writer) {
            $writer->close();
        }, $fileName);
    }

    /**
     * Download products without image used in orders export
     */
    public function downloadProductsWithoutImageUsedInOrders(): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $products = Product::whereHas('orders')
            ->whereNull('image')
            ->get();
        $fileName = 'products-without-image-used-in-orders.xlsx';

        $writer = SimpleExcelWriter::streamDownload($fileName);

        foreach ($products as $product) {
            $writer->addRow([
                'id' => $product->id,
                'sku' => $product->sku,
            ]);
        }

        return response()->streamDownload(function () use ($writer) {
            $writer->close();
        }, $fileName);
    }
}
