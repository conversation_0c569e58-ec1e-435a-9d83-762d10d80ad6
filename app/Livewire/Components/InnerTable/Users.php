<?php

namespace App\Livewire\Components\InnerTable;

use App\Models\User;
use Flux\Flux;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\Computed;

#[Lazy]
class Users extends Component
{
    use WithPagination;

    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public $selectableUsers = [];
    public $selectedUserIds = [];

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        $this->fetchSelectableUsers();

        return view('livewire.components.inner-table.users', [
            'users' => $this->users,
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function users()
    {
        return $this->resourceValue->users()->orderBy('id', 'desc')->paginate(5, pageName: 'client-users-page');
    }

    public function fetchSelectableUsers()
    {
        $this->selectableUsers =
            User::where('id', '!=', auth()->id())
                ->whereNotIn('id', $this->resourceValue->users->pluck('id'))
                ->get();
    }

    public function resetForm(): void
    {
        $this->selectableUsers = [];
        $this->selectedUserIds = [];
        $this->resetValidation();
    }

    public function attach(): void
    {
        // Validate
        $rules = [
            'selectedUserIds' => 'required|array|min:1',
            'selectedUserIds.*' => 'exists:users,id',
        ];
        $messages = [
            'selectedUserIds.required' => 'Please select at least one user to attach.',
            'selectedUserIds.*.exists' => 'The selected user does not exist.',
        ];
        $this->validate($rules, $messages);

        // Attach users
        foreach ($this->selectedUserIds as $userId) {
            // Check if the user is already attached
            if (!$this->resourceValue->users()->where('user_id', $userId)->exists()) {
                $this->resourceValue->users()->attach($userId, ['relation_type' => 'manual']);
            }
        }

        Flux::toast(
            variant: 'success',
            text: 'USERs have been attached.'
        );

        Flux::modals()->close();

        $this->resetPage();
        $this->resetForm();
    }

    public function detach($id): void
    {
        $user = User::findOrFail($id);
        $this->resourceValue->users()->detach($id);

        Flux::toast(
            variant: 'success',
            text: 'The USER has been detached.'
        );

        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('users.show', ['user' => $id], navigate: true);
    }
}
