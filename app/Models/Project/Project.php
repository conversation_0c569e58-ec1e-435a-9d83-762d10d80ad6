<?php

namespace App\Models\Project;

use App\Models\Scopes\UserRoleScope;
use App\Models\User;
use App\Models\Asset;
use App\Models\Client;
use App\Models\Contact;
use App\Models\Partner;
use App\Enums\ProjectTypes;
use App\Models\Order\Order;
use App\Models\Collaborator;
use App\Enums\ProjectStatuses;
use App\Enums\ProjectCodeRegions;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

#[ScopedBy([UserRoleScope::class])]
class Project extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date',
        'status',
        'name',
        'project_code',
        'project_code_progressive',
        'project_code_date',
        'project_code_region',

        'internal_referent_id',
        'area_manager_id',

        'client_id',
        'partner_id',
        'invoicing_address_id',
        'shipping_address_id',
        'payment_term_id',

        'description',
        'type',
        'type_name',
        'type_link',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => ProjectStatuses::class,
        'date' => 'date',
        'project_code_date' => 'date',
        'project_code_region' => ProjectCodeRegions::class,
        'type' => ProjectTypes::class,
    ];

    /**
     * Get the orders for the project.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get all the users for the project.
     */
    public function users(): MorphToMany
    {
        return $this->morphToMany(User::class, 'userable')->withPivot('relation_type');
    }

    /**
     * Get the graphic presentations for the project.
     */
    public function graphicPresentations(): HasMany
    {
        return $this->hasMany(GraphicPresentation::class);
    }

    /**
     * Get all the collaborators for the project.
     */
    public function collaborators(): MorphMany
    {
        return $this->morphMany(Collaborator::class, 'collaboratable');
    }

    /**
     * Get the internal referent for the project.
     */
    public function internalReferent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'internal_referent_id');
    }

    /**
     * Get the area manager for the project.
     */
    public function areaManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'area_manager_id');
    }

    /**
     * Get the client referents for the project.
     */
    public function clientReferents()
    {
        return $this->belongsToMany(Contact::class, 'contact_order');
    }

    /**
     * Get the client for the project.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    /**
     * Get the partner for the project.
     */
    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class, 'partner_id');
    }

    /**
     * Get all the assets for the project.
     */
    public function assets(): MorphMany
    {
        return $this->morphMany(Asset::class, 'assetable');
    }
}
