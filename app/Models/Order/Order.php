<?php

namespace App\Models\Order;

use App\Models\Scopes\UserRoleScope;
use App\Models\User;
use App\Models\Asset;
use App\Models\Client;
use App\Enums\VatTypes;
use App\Models\Address;
use App\Models\Contact;
use App\Models\Partner;
use App\Models\PaymentTerm;
use App\Enums\OrderStatuses;
use App\Models\Collaborator;
use App\Enums\OrderCodeTypes;
use App\Enums\OrderCodeRegions;
use App\Models\Project\Project;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

#[ScopedBy([UserRoleScope::class])]
class Order extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'project_id',

        'date',
        'status',
        'code_progressive',
        'code',
        'description',
        'order_code',
        'order_code_progressive',
        'order_code_date',
        'order_code_type',
        'order_code_region',
        'confirmation_file',

        'internal_referent_id',
        'area_manager_id',

        'client_id',
        'partner_id',
        'invoicing_address_id',
        'shipping_address_id',
        'payment_term_id',
        'vat_type'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => OrderStatuses::class,
        'date' => 'date',
        'order_code_date' => 'date',
        'order_code_type' => OrderCodeTypes::class,
        'order_code_region' => OrderCodeRegions::class,
        'vat_type' => VatTypes::class,
    ];

    /**
     * Get order total amount.
     */
    public function getTotalAmountAttribute(?bool $withAddons = false, ?bool $withVat = false): float
    {
        $total = $this->orderRows->sum(function ($orderRow) {
            return $orderRow->rowFinalPrice;
        });

        if ($withAddons) {
            $addonsTotal = $this->addons->sum(function ($addon) use ($total) {
                return $total * ($addon->percent / 100);
            });
            $total += $addonsTotal;
        }

        if ($withVat) {
            $total += $total * $this->vat_type->rate();
        }

        return $total;
    }

    /**
     * Get addons amount.
     */
    public function getAddonsAmountAttribute(): float
    {
        $total = $this->orderRows->sum(function ($orderRow) {
            return $orderRow->rowFinalPrice;
        });

        return $this->addons->sum(function ($addon) use ($total) {
            return $total * ($addon->percent / 100);
        });
    }

    /**
     * Get VAT total on the order.
     */
    public function getVatTotalAttribute(?bool $withAddons = false): float
    {
        return $this->getTotalAmountAttribute($withAddons, $withVat = false) * $this->vat_type->rate();
    }

    /**
     * Get the order payments.
     */
    public function getPaymentsAttribute(): array
    {
        if (!$this->paymentTerm || !$this->paymentTerm->items) {
            return [];
        }

        return $this->paymentTerm->items->map(function ($item) {
            return [
                'description' => $item->description,
                'percentage' => $item->percentage,
                'value' => $this->getTotalAmountAttribute($withAddons = true, $withVat = true) * ($item->percentage / 100),
                'formatted_value' => '€ ' . number_format($this->getTotalAmountAttribute($withAddons = true, $withVat = true) * ($item->percentage / 100), 2, ',', '.')
            ];
        })->toArray();
    }

    /**
     * Get the cart groups for the order.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cartGroups(): HasMany
    {
        return $this->hasMany(CartGroup::class);
    }

    /**
     * Get the order rows for the order.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function orderRows(): HasManyThrough
    {
        return $this->hasManyThrough(OrderRow::class, CartGroup::class);
    }

    /**
     * Get the project that owns the order.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get all the collaborators for the order.
     */
    public function collaborators(): MorphMany
    {
        return $this->morphMany(Collaborator::class, 'collaboratable');
    }

    /**
     * Get the internal referent for the order.
     */
    public function internalReferent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'internal_referent_id');
    }

    /**
     * Get the area manager for the order.
     */
    public function areaManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'area_manager_id');
    }

    /**
     * Get the client referents for the order.
     */
    public function clientReferents()
    {
        return $this->belongsToMany(Contact::class, 'contact_order');
    }

    /**
     * Get the client for the order.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    /**
     * Get the partner for the order.
     */
    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class, 'partner_id');
    }

    /**
     * Get all the assets for the order.
     */
    public function assets(): MorphMany
    {
        return $this->morphMany(Asset::class, 'assetable');
    }

    /**
     * Get all the assets retated to the order.
     */
    public function relatedAssets(): MorphMany
    {
        return $this->morphMany(Asset::class, 'relatable');
    }

    /**
     * Get the invoicing address for the order.
     */
    public function invoicingAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'invoicing_address_id');
    }

    /**
     * Get the shipping address for the order.
     */
    public function shippingAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'shipping_address_id');
    }

    /**
     * Get all the addresses for the order.
     */
    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'addressable');
    }

    /**
     * Check if the order has cart groups.
     */
    public function hasCartGroups(): bool
    {
        return $this->cartGroups()->exists();
    }

    /**
     * Get the addons for the order.
     */
    public function addons(): HasMany
    {
        return $this->hasMany(OrderAddon::class);
    }

    /**
     * Get the payment term for the order.
     */
    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    /**
     * Get all the users for the order.
     */
    public function users(): MorphToMany
    {
        return $this->morphToMany(User::class, 'userable')->withPivot('relation_type');
    }

    /**
     * Get all products in this order.
     */
    public function products()
    {
        return $this->hasManyThrough(
            \App\Models\Product::class,
            OrderRow::class,
            'cart_group_id', // Foreign key on OrderRow table
            'id', // Foreign key on Product table
            'id', // Local key on Order table
            'product_id' // Local key on OrderRow table
        )->join('cart_groups', 'cart_groups.order_id', '=', 'orders.id')
         ->whereNotNull('order_rows.product_id');
    }
}
