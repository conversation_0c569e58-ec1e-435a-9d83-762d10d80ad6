<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Contact: {{ $contact->name }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the contact info and details</flux:subheading>

    <div class="flex flex-col gap-12 my-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div class="flex items-center">
                </div>
                <div>
                    @canany(['update', 'delete'], $contact)
                        <flux:dropdown>
                            <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                            <flux:menu>
                                @can('update', $contact)
                                    <flux:menu.item wire:click="edit({{ $contact->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan
                                @can('delete', $contact)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this contact? This action cannot be undone." wire:click="delete({{ $contact->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    @endcanany
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Contact details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the contact info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.name" readonly variant="filled" label="Name" description="This will be publicly displayed." />
                    <div></div>
                    <flux:input wire:model="form.email" readonly copyable variant="filled" label="Email" description="This will be publicly displayed." />
                    <flux:input wire:model="form.phone" readonly copyable variant="filled" label="Phone" description="This will be publicly displayed." />
                    <flux:field>
                        <flux:select searchable wire:model.live="form.departments" variant="listbox" disabled clearable multiple placeholder="Choose the contact departments..." label="Departments" description="This will be publicly displayed." badge="Optional">
                            @foreach ($departments as $department)
                                <flux:select.option value="{{ $department->value }}">{{ $department->label() }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:field>
                    <flux:input wire:model="form.position" type="text" placeholder="Position" readonly variant="filled" label="Position" description="This will be publicly displayed." badge="Optional" />
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Country and languages details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the contact info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:select searchable wire:model="form.country" variant="listbox" disabled searchable placeholder="Choose country for the contact..." label="Country" description="This will be publicly displayed." badge="Optional">
                        @foreach($countries as $country)
                            <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:field>
                        <flux:select searchable wire:model.live="form.languages" variant="listbox" disabled searchable clearable multiple placeholder="Choose languages the contact speak..." label="Languages" description="This will be publicly displayed." badge="Optional">
                            @foreach ($languages as $language)
                                <flux:select.option value="{{ $language->value }}">{{ $language->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:field>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the contact extra info</flux:subheading>
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 gap-4">
                    <flux:textarea wire:model="form.notes" readonly variant="filled" label="Notes" badge="Optional" description="This will be publicly displayed." />
                </div>
            </div>
        </div>
    </div>

    <flux:separator variant="subtle" class="my-12" />

    {{-- Brands Listing --}}
    <livewire:components.inner-table.brands :resourceType="class_basename($contact)" :resourceValue="$contact" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Clients Listing --}}
    <livewire:components.inner-table.clients :resourceType="class_basename($contact)" :resourceValue="$contact" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Partners Listing --}}
    <livewire:components.inner-table.partners :resourceType="class_basename($contact)" :resourceValue="$contact" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Suppliers Listing --}}
    <livewire:components.inner-table.suppliers :resourceType="class_basename($contact)" :resourceValue="$contact" >

</flux:main>
