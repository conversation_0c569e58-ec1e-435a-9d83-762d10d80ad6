<flux:header container class="bg-zinc-50 dark:bg-zinc-900 border-b border-zinc-200 dark:border-zinc-700">
    <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

    <flux:brand logo="https://fluxui.dev/img/demo/logo.png" name="{{ config('app.name') }}" class="max-lg:hidden dark:hidden" />
    <flux:brand logo="https://fluxui.dev/img/demo/dark-mode-logo.png" name="{{ config('app.name') }}" class="max-lg:hidden! hidden dark:flex" />

    <flux:navbar class="-mb-px max-lg:hidden">
        {{-- Home --}}
        <flux:navbar.item icon="home" href="{{ route('home') }}" wire:navigate>Home</flux:navbar.item>

        {{-- Orders --}}
        @can('viewAny', App\Models\Order::class)
            <flux:navbar.item icon="inbox" href="{{ route('orders.index') }}" wire:navigate>Orders</flux:navbar.item>
        @endcan

        {{-- Projects --}}
        @can('viewAny', App\Models\Project::class)
            <flux:navbar.item icon="book-open" href="{{ route('projects.index') }}" wire:navigate>Projects</flux:navbar.item>
        @endcan

        {{-- Clients --}}
        @can('viewAny', App\Models\Client::class)
            <flux:navbar.item icon="building-storefront" href="{{ route('clients.index') }}" wire:navigate>Clients</flux:navbar.item>
        @endcan

        {{-- Partners --}}
        @can('viewAny', App\Models\Partner::class)
            <flux:navbar.item icon="cube-transparent" href="{{ route('partners.index') }}" wire:navigate>Partners</flux:navbar.item>
        @endcan

        {{-- Brands --}}
        @can('viewAny', App\Models\Brand::class)
            <flux:navbar.item icon="swatch" href="{{ route('brands.index') }}" wire:navigate>Brands</flux:navbar.item>
        @endcan
    </flux:navbar>

    {{-- Mobile - Main Menù --}}
    <flux:sidebar stashable sticky class="lg:hidden bg-zinc-50 dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-700">
        <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

        <flux:brand href="#" logo="https://fluxui.dev/img/demo/logo.png" name="{{ config('app.name') }}" class="px-2 dark:hidden" />
        <flux:brand href="#" logo="https://fluxui.dev/img/demo/dark-mode-logo.png" name="{{ config('app.name') }}" class="px-2 hidden dark:flex" />

        <flux:navlist variant="outline">
            {{-- Home --}}
            <flux:navlist.item icon="home" href="{{ route('home') }}" wire:navigate>Home</flux:navlist.item>

            {{-- Orders --}}
            @can('viewAny', App\Models\Order::class)
                <flux:navbar.item icon="inbox" href="{{ route('orders.index') }}" wire:navigate>Orders</flux:navbar.item>
            @endcan

            {{-- Projects --}}
            @can('viewAny', App\Models\Project::class)
                <flux:navbar.item icon="book-open" href="{{ route('projects.index') }}" wire:navigate>Projects</flux:navbar.item>
            @endcan

            {{-- Clients --}}
            @can('viewAny', App\Models\Client::class)
                <flux:navlist.item icon="building-storefront" href="{{ route('clients.index') }}" wire:navigate>Clients</flux:navlist.item>
            @endcan

            {{-- Partners --}}
            @can('viewAny', App\Models\Partner::class)
                <flux:navlist.item icon="cube-transparent" href="{{ route('partners.index') }}" wire:navigate>Partners</flux:navlist.item>
            @endcan

            {{-- Brands --}}
            @can('viewAny', App\Models\Brand::class)
                <flux:navlist.item icon="swatch" href="{{ route('brands.index') }}" wire:navigate>Brands</flux:navlist.item>
            @endcan

        </flux:navlist>

        <flux:spacer />

        <flux:navlist variant="outline">
            {{-- Imports --}}
            <flux:navlist.item icon="cloud-arrow-up" href="{{ route('imports.index') }}" wire:navigate class="flex md:hidden">Import</flux:navlist.item>

            {{-- Exports --}}
            <flux:navlist.item icon="cloud-arrow-down" href="{{ route('exports.index') }}" wire:navigate class="flex md:hidden">Export</flux:navlist.item>

            {{-- Help --}}
            <flux:navlist.item icon="information-circle" href="#">Help</flux:navlist.item>
        </flux:navlist>
    </flux:sidebar>

    <flux:spacer />

    <flux:navbar>

        {{-- Contacts --}}
        @can('viewAny', App\Models\Contact::class)
            <flux:navbar.item href="{{ route('contacts.index') }}" wire:navigate size="sm" variant="subtle">
                <flux:icon.at-symbol variant="mini" />
            </flux:navbar.item>
        @endcan

        {{-- Command Palette --}}
        <flux:modal.trigger name="search" shortcut="cmd.k">
            <flux:button size="sm" variant="subtle">
                <flux:icon.squares-plus variant="mini" />
            </flux:button>
        </flux:modal.trigger>

        <flux:modal name="search" variant="bare" class="min-h-[30rem] w-full max-w-[30rem] px-6" x-on:keydown.cmd.k.document="$el.showModal()">
            <flux:command class="border-none shadow-lg">
                <flux:command.input placeholder="Search command..." closable />

                <flux:command.items>
                    @can('create', App\Models\Client::class)
                        <flux:command.item wire:click="$dispatch('redirect-to-client-create')" icon="building-storefront" class="cursor-pointer">Create new client</flux:command.item>
                    @endcan
                    @can('create', App\Models\Partner::class)
                        <flux:command.item wire:click="$dispatch('redirect-to-partner-create')" icon="cube-transparent" class="cursor-pointer">Create new partner</flux:command.item>
                    @endcan
                    @can('create', App\Models\User::class)
                        <flux:command.item wire:click="$dispatch('redirect-to-user-create')" icon="user-plus" class="cursor-pointer">Create new user</flux:command.item>
                    @endcan
                    <flux:command.item icon="cloud-arrow-up">Imports</flux:command.item>
                    <flux:command.item icon="cloud-arrow-down">Exports</flux:command.item>
                    <flux:command.item icon="book-open">Documentation</flux:command.item>
                    <flux:command.item icon="cog-6-tooth" kbd="⌘,">Settings</flux:command.item>
                </flux:command.items>
            </flux:command>
        </flux:modal>

        <flux:separator vertical variant="subtle" class="my-2"/>

        {{-- Imports --}}
        <flux:navbar.item href="{{ route('imports.index') }}" wire:navigate size="sm" variant="subtle" class="hidden md:flex">
            <flux:icon.cloud-arrow-up variant="mini" />
        </flux:navbar.item>

        {{-- Exports --}}
        <flux:navbar.item href="{{ route('exports.index') }}" wire:navigate size="sm" variant="subtle" class="hidden md:flex">
            <flux:icon.cloud-arrow-down variant="mini" />
        </flux:navbar.item>

        <flux:separator vertical variant="subtle" class="hidden md:flex my-2"/>

        {{-- Data --}}
        <flux:dropdown>
            <flux:navbar.item icon="circle-stack" icon-trailing="chevron-down"></flux:navbar.item>
            <flux:navmenu>
                <flux:navlist>
                    <flux:navlist.group heading="Catalogs" class="">
                        @can('viewAny', App\Models\Brand::class)
                            <flux:navlist.item href="{{ route('brands.index') }}" wire:navigate>Brands</flux:navmenu.item>
                        @endcan
                        @can('viewAny', App\Models\Collection::class)
                            <flux:navlist.item href="{{ route('collections.index') }}" wire:navigate>Collections</flux:navmenu.item>
                        @endcan
                        @can('viewAny', App\Models\Product::class)
                            <flux:navlist.item href="{{ route('products.index') }}" wire:navigate>Products</flux:navmenu.item>
                        @endcan
                    </flux:navlist.group>
                </flux:navlist>
                <flux:navlist>
                    <flux:navlist.group heading="Suppliers" class="mt-4">
                        @can('viewAny', App\Models\DiscountGroup::class)
                            <flux:navlist.item href="{{ route('discount-groups.index') }}" wire:navigate>Discount Group</flux:navmenu.item>
                        @endcan
                        @can('viewAny', App\Models\Supplier::class)
                            <flux:navlist.item href="{{ route('suppliers.index') }}" wire:navigate>Suppliers</flux:navmenu.item>
                        @endcan
                    </flux:navlist.group>
                </flux:navlist>
                <flux:navlist>
                    <flux:navlist.group heading="Users and Roles" class="mt-4">
                        @can('viewAny', App\Models\User::class)
                            <flux:navlist.item href="{{ route('users.index') }}" wire:navigate>Users</flux:navmenu.item>
                        @endcan
                        @can('viewAny', Spatie\Permission\Models\Role::class)
                            <flux:navlist.item href="{{ route('roles.index') }}" wire:navigate>Roles and Permissions</flux:navmenu.item>
                        @endcan
                    </flux:navlist.group>
                </flux:navlist>
                <flux:navlist>
                    <flux:navlist.group heading="Tools" class="mt-4">
                        <flux:navlist.item href="http://data.bergomiinteriors.com" target="_blank">Enrichment Data</flux:navmenu.item>
                    </flux:navlist.group>
                </flux:navlist>
            </flux:navmenu>
        </flux:dropdown>

        <flux:separator vertical variant="subtle" class="my-2"/>

        <flux:button x-data x-on:click="$flux.dark = ! $flux.dark" icon="moon" variant="subtle" aria-label="Toggle dark mode" />

        <flux:navbar.item wire:click="logout" icon="arrow-right-end-on-rectangle" label="Logout" />
    </flux:navbar>
</flux:header>
