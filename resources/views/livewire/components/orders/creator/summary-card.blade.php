{{-- Car<PERSON> and Summary --}}
<div class="flex flex-col gap-2">
    <flux:card size="sm" class="flex flex-col justify-between">
        <div>
            <flux:subheading class="mb-1">Cart details</flux:subheading>
            <flux:separator class="mb-2"></flux:separator>
        </div>
        <div>
            <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">Amount</span>{{ eu_currency($order->totalAmount) }}</flux:heading>
            <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">With addons</span>{{ eu_currency($order->getTotalAmountAttribute($withAddons = true)) }}</flux:heading>
        </div>
    </flux:card>

    <div class="flex gap-2">
        <flux:button @click="$dispatch('show-cart')" icon="shopping-bag" variant="primary" size="sm" class="w-full">Show cart</flux:button>
        <flux:dropdown>
            <flux:button icon-trailing="chevron-down" size="sm" class="w-full">Actions</flux:button>
            <flux:menu>
                <flux:menu.item @click="$dispatch('order-download-pdf')" icon="document-text">Export Order to PDF</flux:menu.item>
                <flux:menu.item @click="$dispatch('order-download-excel')" icon="table-cells">Export Order to XLSX</flux:menu.item>
                <flux:menu.item @click="$dispatch('order-rows-download-excel')" icon="table-cells">Export Order Rows to XLSX</flux:menu.item>
            </flux:menu>
        </flux:dropdown>
    </div>
</div>